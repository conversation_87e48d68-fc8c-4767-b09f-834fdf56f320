#!/usr/bin/env python3
"""
Captcha Type Detection Tool

This tool captures screenshots of captcha areas and uses AI vision to detect
the type of captcha present (slider vs checkbox/checkmark).

Features:
- Screenshot capture using existing captcha solver coordinates
- AI-powered captcha type classification
- Binary detection results (slider yes/no, checkbox yes/no)
- Standalone operation focused on captcha type identification

Usage:
    python captcha_type_detector.py
    
Returns:
    Dictionary with detection results for slider and checkbox captcha types
"""

import requests
import json
import base64
from PIL import ImageGrab
import time
from typing import Optional, Dict, Any, Tuple


class CaptchaTypeDetector:
    def __init__(self, captcha_bbox: Optional[Tuple[int, int, int, int]] = None):
        """
        Initialize the captcha type detector.
        
        Args:
            captcha_bbox: Optional custom bounding box (left, top, right, bottom)
                         If None, uses default coordinates from ai.py
        """
        # Use same API configuration as ai.py
        self.api_key = "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"
        self.api_url = "https://openrouter.ai/api/v1"
        
        # Use same captcha bounding box as ai.py or custom one
        if captcha_bbox:
            self.captcha_bbox = captcha_bbox
        else:
            # Default coordinates from ai.py (Demo Website)
            self.captcha_bbox = (1401, 490, 1731, 751)  # (left, top, right, bottom)
        
        print("🔍 Captcha Type Detector Initialized")
        print(f"📍 Using captcha region: {self.captcha_bbox}")

    def capture_screenshot(self) -> str:
        """
        Capture screenshot of the captcha area.
        
        Returns:
            str: Path to the saved screenshot file
        """
        screenshot = ImageGrab.grab(bbox=self.captcha_bbox)
        screenshot_path = "captcha_type_detection.png"
        screenshot.save(screenshot_path)
        
        print(f"📸 Screenshot captured: {self.captcha_bbox}")
        print(f"📏 Screenshot size: {screenshot.size}")
        
        return screenshot_path

    def detect_captcha_type_with_ai(self, image_path: str) -> Optional[Dict[str, Any]]:
        """
        Use AI vision to detect captcha type in the screenshot.
        
        Args:
            image_path: Path to the screenshot image
            
        Returns:
            Optional[Dict]: API response or None if failed
        """
        with open(image_path, "rb") as image_file:
            base64_image = base64.b64encode(image_file.read()).decode('utf-8')

        payload = {
            "model": "qwen/qwen2.5-vl-32b-instruct",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": """Analyze this image to detect if a slider element exists. Look for:

SLIDER ELEMENT:
- A horizontal slider/drag component
- A sliding button or handle that can be moved
- A track or rail for sliding motion
- Any draggable interface element
- Slider controls or verification sliders

You MUST respond with ONLY valid JSON in this exact format (no other text):
{
  "slider_present": true,
  "confidence": "high",
  "description": "brief description of what you see in the image"
}

Replace true with false if no slider is found. Replace "high" with "medium" or "low" based on your confidence. Be precise - return true only if you clearly see a slider element that can be dragged."""
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            "temperature": 0.1
        }

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        try:
            print("🤖 Sending image to AI for captcha type detection...")
            response = requests.post(f"{self.api_url}/chat/completions", headers=headers, json=payload)
            
            print(f"📡 Response status: {response.status_code}")
            
            if response.status_code != 200:
                print(f"❌ API Error: {response.status_code}")
                print(f"Response: {response.text}")
                return None

            if not response.text.strip():
                print("❌ Empty response from API")
                return None

            try:
                json_response = response.json()
                print(f"🔍 Raw API response structure: {list(json_response.keys())}")
                return json_response
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse API response as JSON: {e}")
                print(f"Raw response text: {response.text}")
                return None

        except Exception as e:
            print(f"❌ Error calling AI API: {e}")
            return None

    def parse_detection_response(self, response: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Parse the AI API response and extract captcha type detection results.

        Args:
            response: Raw API response

        Returns:
            Optional[Dict]: Parsed detection results or None if failed
        """
        try:
            # Check if response has the expected structure
            if "choices" not in response or len(response["choices"]) == 0:
                print("❌ Invalid response structure: no choices found")
                print(f"Full response: {response}")
                return None

            content = response["choices"][0]["message"]["content"]
            print(f"🤖 AI Response: {content}")

            # Check if content is empty or None
            if not content or content.strip() == "":
                print("❌ Empty content in AI response")
                return None

            # Extract JSON from response
            if "```json" in content:
                json_str = content.split("```json")[1].split("```")[0].strip()
            elif "{" in content and "}" in content:
                # Try to extract JSON from content
                start = content.find("{")
                end = content.rfind("}") + 1
                json_str = content[start:end].strip()
            else:
                json_str = content.strip()

            print(f"🔍 Extracted JSON string: {json_str}")

            if not json_str:
                print("❌ No JSON content found in response")
                return None

            data = json.loads(json_str)
            
            # Validate response structure
            required_fields = ["slider_present", "confidence", "description"]
            for field in required_fields:
                if field not in data:
                    print(f"❌ Missing required field: {field}")
                    return None

            # Validate boolean field
            if not isinstance(data["slider_present"], bool):
                print("❌ Invalid boolean value for slider detection")
                return None

            # Validate confidence level
            if data["confidence"] not in ["high", "medium", "low"]:
                print("❌ Invalid confidence level")
                return None

            print(f"✅ Slider element detected: {data['slider_present']}")
            print(f"🎯 Detection confidence: {data['confidence']}")
            print(f"📋 Description: {data['description']}")
            
            return data
            
        except json.JSONDecodeError as e:
            print(f"❌ JSON decode error: {e}")
            print(f"Response content: {content}")
            print(f"Attempted to parse: {json_str}")
            return None
        except KeyError as e:
            print(f"❌ Missing key in response: {e}")
            print(f"Full response: {response}")
            return None
        except Exception as e:
            print(f"❌ Error parsing response: {e}")
            print(f"Response type: {type(response)}")
            print(f"Response: {response}")
            return None

    def detect_captcha_type(self) -> Optional[Dict[str, Any]]:
        """
        Complete captcha type detection process.
        
        Returns:
            Optional[Dict]: Detection results with slider_present, confidence,
                           and description fields
        """
        print("🚀 Starting Captcha Type Detection")
        print("=" * 50)
        
        # Step 1: Capture screenshot
        print("📸 Step 1: Capturing screenshot...")
        image_path = self.capture_screenshot()
        
        # Step 2: Detect captcha type with AI
        print("🤖 Step 2: Analyzing captcha type with AI...")
        response = self.detect_captcha_type_with_ai(image_path)
        if not response:
            print("❌ Failed to get AI response")
            return None
        
        # Step 3: Parse detection results
        print("📋 Step 3: Parsing detection results...")
        detection_results = self.parse_detection_response(response)
        
        if detection_results:
            print("=" * 50)
            print("🎉 SLIDER DETECTION COMPLETED!")
            print(f"🎚️  Slider element: {'YES' if detection_results['slider_present'] else 'NO'}")
            print(f"🎯 Confidence: {detection_results['confidence'].upper()}")
            print("=" * 50)
        
        return detection_results

    def set_captcha_region(self, bbox: Tuple[int, int, int, int]) -> None:
        """
        Update the captcha bounding box region.
        
        Args:
            bbox: New bounding box (left, top, right, bottom)
        """
        self.captcha_bbox = bbox
        print(f"📍 Updated captcha region: {self.captcha_bbox}")


def detect_captcha_type_standalone(captcha_bbox: Optional[Tuple[int, int, int, int]] = None) -> Optional[Dict[str, Any]]:
    """
    Standalone function for captcha type detection.
    
    Args:
        captcha_bbox: Optional custom bounding box (left, top, right, bottom)
        
    Returns:
        Optional[Dict]: Detection results or None if failed
    """
    captcha_bbox = (751, 402, 1169, 730)
    slide_button_coords = (713, 712)
    detector = CaptchaTypeDetector(captcha_bbox)
    return detector.detect_captcha_type()


def main():
    """Main function for command-line usage"""
    print("🔍 Captcha Type Detector - Standalone Mode")
    
    # Run detection with default settings
    results = detect_captcha_type_standalone()
    
    if results:
        print("\n📊 FINAL RESULTS:")
        print(f"   🎚️  Slider element present: {results['slider_present']}")
        print(f"   🎯 Confidence level: {results['confidence']}")
        print(f"   📝 Description: {results['description']}")

        # Determine result
        if results['slider_present']:
            print("   ✅ RESULT: Slider element detected")
        else:
            print("   ❌ RESULT: No slider element found")
    else:
        print("❌ Slider detection failed")


if __name__ == "__main__":
    main()
